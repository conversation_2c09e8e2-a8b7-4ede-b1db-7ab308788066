import { ChatMessage } from '@/models/chat';
import { Button, Spin } from '@douyinfe/semi-ui';
import ImgGeneratingRender from './component/ImgGenerating';
import styles from './index.less';

const LoadingRender: React.FC<{ appCode: string; message: ChatMessage; imgCount?: number }> = ({
  appCode,
  message,
  imgCount = 1,
}) => {
  const isImgGenerating = ['image'].includes(appCode) && imgCount;
  const status = message.progressInfo?.name === '生成中' ? 'generating' : 'loading';
  return (
    <div className={styles.loadingContainer}>
      <Button theme="borderless" type="tertiary" className={styles.isReasoningLoading}>
        <Spin size="small" />
        <div style={{ marginLeft: '4px' }}>{message.progressInfo?.name || '正在处理中'}</div>
      </Button>
      {isImgGenerating ? (
        <ImgGeneratingRender
          status={status}
          count={imgCount}
          progress={message.progressInfo?.progress || 0}
        />
      ) : (
        ''
      )}
    </div>
  );
};
export default LoadingRender;
