import { NONE_SHOW, SPECIAL_NAMES } from '@/config';
import { ImgContentData, MergedItem } from '@/pages/Chat/types';
import { Descendant } from 'slate';

export const generateRandomString = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 将纯文本转换为富文本输入格式
 * 该函数主要用于解析文本中的标签，并将其转换为可编辑的富文本格式
 *
 * @param text 输入的纯文本字符串
 * @returns 返回转换后的富文本子元素数组
 */
export const convertTextToRichInput = (text: string): Descendant[] => {
  // 定义正则表达式，用于匹配文本中的标签
  const regex = /\[(.*?)\]/g;
  let match;
  let lastIndex = 0;
  const children: any[] = [];

  while ((match = regex.exec(text)) !== null) {
    const [fullMatch, content] = match;

    // 添加普通文本部分
    if (match.index > lastIndex) {
      children.push({
        text: text.substring(lastIndex, match.index),
      });
    }

    // 添加 labeled_input 节点
    children.push({
      type: 'labeled_input',
      placeholder: `${content}`,
      key: `label-${match.index}`,
      editable: true,
      children: [{ text: '' }],
      focusOnClick: true,
    });

    lastIndex = match.index + fullMatch.length;
  }

  // 添加结尾剩余的文本
  if (lastIndex < text.length) {
    children.push({
      text: text.substring(lastIndex),
    });
  }

  return [
    {
      type: 'paragraph',
      children: children,
    },
  ];
};

export const fileOnlinePreview = (url = '', target = '_blank') => {
  const base64EncodedUrl = btoa(unescape(encodeURIComponent(url)));
  const enCodeUrl = encodeURIComponent(base64EncodedUrl);
  const previewUrl = 'https://fileview.bhidi.com/onlinePreview';
  window.open(`${previewUrl}?url=${enCodeUrl}`, target);
};

function dataURItoBlob(base64: any) {
  let byteString;
  const arr = base64.split(',');
  if (arr[0].indexOf('base64') !== -1) {
    byteString = atob(arr[1]);
  } else {
    byteString = unescape(arr[1]);
  }

  const mimeString = arr[0].split(':')[1].split(';')[0];
  const ia = new Uint8Array(byteString.length);
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }

  return new Blob([ia], { type: mimeString });
}

export { dataURItoBlob };
// 封装添加前缀的函数
export const addInternationalProjectPrefix = (desc: string) => {
  if (desc === '国际工程制度' || desc === '国际工程合同') {
    return '国际工程-' + desc;
  }
  return desc;
};

// 封装移除前缀的函数
export const removeInternationalProjectPrefix = (desc: string) => {
  if (desc === '国际工程-国际工程制度' || desc === '国际工程-国际工程合同') {
    return desc.split('-')[1];
  }
  return desc;
};
export const preprocessLaTeX = (content: string) => {
  if (typeof content !== 'string') return content;
  return content
    .replace(/\\\[(.*?)\\\]/gs, (_, equation) => `$$${equation}$$`)
    .replace(/\\\((.*?)\\\)/gs, (_, equation) => `$$${equation}$$`)
    .replace(/(^|[^\\])\$(.+?)\$/gs, (_, prefix, equation) => `${prefix}$${equation}$`)
    .replace(/\t/g, '\\t');
};

// 从URL中提取文件名
export const getFilenameFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    return pathname.substring(pathname.lastIndexOf('/') + 1) || 'download';
  } catch {
    return 'download';
  }
};

// 文件下载
export const downloadFile = async (url: string, name: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const blob = await response.blob();
  const downloadUrl = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = name || getFilenameFromUrl(url);
  document.body.appendChild(link);
  link.click();
  URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

// 移除文字#号后的内容 123#1 => 123
export const replaceTextHash = (text: string): string => {
  const regex = /([^#]+)#.*/;
  return text.replace(regex, '$1');
};
// 移除文字后面的数字 你好吗1 => 你好吗
export const replaceTextNumber = (text: string): string => {
  const regex = /([^\d]+)\d+$/;
  return text.replace(regex, '$1');
};

// 过滤对话流接口中特殊字符
export const processFlowNodeStatusText = (text: string, flowNodeStatus: any): string => {
  let newText = text;

  if (text.includes('#')) {
    newText = replaceTextHash(newText);
  }

  if (/\d/.test(newText)) {
    newText = replaceTextNumber(newText);
  }

  if (NONE_SHOW.hasOwnProperty(newText)) {
    newText = NONE_SHOW[flowNodeStatus.name as keyof typeof NONE_SHOW];
  }

  if (SPECIAL_NAMES.hasOwnProperty(newText)) {
    newText = SPECIAL_NAMES[flowNodeStatus.name as keyof typeof SPECIAL_NAMES];
  }

  return newText;
};

// 搜索来源相同 collectionId 合并处理
export const mergeByCollectionId = (
  data: {
    collectionId: string;
    datasetId: string;
    hlQ: string | string[];
    hlSourceName: string;
    q: string | string[];
    sourceId: string;
    sourceName: string;
    xquery: string;
  }[],
): MergedItem[] => {
  // 空数组处理
  if (!Array.isArray(data)) {
    return [];
  }

  return data
    .reduce((acc: MergedItem[], item) => {
      const existingItem = acc.find((accItem) => accItem.collectionId === item.collectionId);
      const currentHlQ = Array.isArray(item.hlQ) ? item.hlQ.join(' ') : item.hlQ;

      if (existingItem) {
        // 合并已有 hlQ 字符串和当前 hlQ 字符串
        existingItem.hlQ = [existingItem.hlQ, currentHlQ].join(' ').trim();
      } else {
        acc.push({ ...item, hlQ: currentHlQ });
      }
      return acc;
    }, [])
    .reduce((acc: MergedItem[], item) => {
      // 第一次去重：通过 sourceId
      const existingItemById = acc.find((accItem) => accItem.sourceId === item.sourceId);
      if (!existingItemById) {
        acc.push(item);
      }
      return acc;
    }, [])
    .reduce((acc: MergedItem[], item) => {
      // 第二次去重：通过 sourceName
      const existingItemByName = acc.find((accItem) => accItem.sourceName === item.sourceName);
      if (!existingItemByName) {
        acc.push(item);
      }
      return acc;
    }, []);
};

// 解析流式数据
export const parseImgContentData = (data: unknown): ImgContentData[] => {
  if (!Array.isArray(data)) return [];
  return data.map((item) => ({
    type: 'image_url',
    image_url: {
      url: item.file.url,
      name: item.file.name,
      size: item.file.size,
      width: item.file.width,
      height: item.file.height,
      type: 'file',
    },
  }));
};

// 格式化时间戳
export const formatTimestamp = (timestamp: string | number | Date) => {
  // 将时间戳转换为Date对象
  const date = new Date(timestamp);
  // 获取当前时间
  const today = new Date();
  // 判断时间戳是否为今天
  const isToday =
    date.getFullYear() === today.getFullYear() &&
    date.getMonth() === today.getMonth() &&
    date.getDate() === today.getDate();

  // 获取小时和分钟，并补全两位数
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  // 如果是今天，返回格式为“今天 HH:mm 完成”
  if (isToday) {
    return `今天 ${hours}:${minutes} 完成`;
  }

  // 获取年份、月份和日期
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  // 返回格式为“MM月dd日 HH:mm 完成”
  return `${year}年${month}月${day}日 ${hours}:${minutes} 完成`;
};

// 定义一个防抖函数，用于限制函数的执行频率
export const debounce = <T>(func: Function, wait: number) => {
  let timeout: ReturnType<typeof setTimeout> | null;
  return function (this: T, ...args: any[]) {
    const context = this;
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
};

// 检查对象的所有值是否都不为空
export const allValuesNotEmpty = (obj: any, emptyValues = [null, undefined, '']) => {
  // 先判断输入是否为有效对象
  if (obj === null || typeof obj !== 'object') {
    return false;
  }

  // 遍历对象的所有自有属性，只要有一个值不在空值列表中，就返回true
  return Object.keys(obj).some((key) => {
    if (key === 'userInput') {
      return false;
    }
    const value = obj[key];
    return !emptyValues.includes(value);
  });
};
