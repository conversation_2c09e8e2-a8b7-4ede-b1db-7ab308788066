import type { ChatContent, ChatMessage } from '@/models/chat';
import { Image, ImagePreview } from '@douyinfe/semi-ui';
import { CSSProperties } from 'react';
import styles from './index.less';

const ImageContentRender: React.FC<{ message: ChatMessage; imageStyle?: CSSProperties }> = ({
  message,
  imageStyle,
}) => {
  // 识别生成的图片是错误的标识
  const errorImageFlag = 'AI:IMAGE:TASK:ERROR';
  const content = typeof message.content === 'string' ? message.content : '';
  return (
    <div className={styles.imageContent}>
      <ImagePreview className={styles.imagePreview}>
        {Array.isArray(message.content || []) && message.content?.length ? (
          (message.content as ChatContent[]).map((item, index) => {
            return (
              <Image
                key={index}
                src={item.image_url?.url}
                alt={`lamp${index + 1}`}
                imgStyle={imageStyle || {}}
                preview={!item.image_url?.name?.includes(errorImageFlag)}
              />
            );
          })
        ) : (
          <p className={styles.noImage}>{content || '暂无图片生成'}</p>
        )}
      </ImagePreview>
    </div>
  );
};
export default ImageContentRender;
