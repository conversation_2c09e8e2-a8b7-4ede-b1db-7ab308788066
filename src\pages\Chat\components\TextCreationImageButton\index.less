.uploadFile {
  width: auto;
  height: auto;
}

.onlineQueryBtn {
  border-radius: 8px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #ebeef2 !important;
  box-sizing: border-box;
  font-weight: normal;

  &:global(.semi-button-tertiary.semi-button-borderless) {
    color: #000;
  }

  &:global(.semi-button-primary.semi-button-borderless) {
    border: none;
    background: rgba(0, 91, 248, 8%);
  }

  :global(.semi-button-content-right) {
    margin-left: 4px;
    font-weight: normal;
  }
}

.itemInfo {
  display: flex;
  height: 40px;
  line-height: 40px;
  justify-content: center;
  align-items: center;

  .itemIcon {
    min-width: 15px;
  }

  .itemColor {
    width: 28px;
    height: 16px;
    border-radius: 4px;
    box-shadow: 0 1px 6px 0 #D9DFEE;
  }

  .itemDescribe {
    margin-left: 14px;
  }
}
