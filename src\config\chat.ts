import logo from '@/assets/logo.svg';

export const DEFAULT_STREAM_CONTENT =
  '抱歉！当前内容无法解析，为了更准确地帮您分析，建议您补充更多信息，我会持续为您提供支持，随时解答疑问！';

export const CHAT_PLACEHOLDER = '请输入您的问题...';

export const DEFAULT_ANSWER = '对不起，我无法理解您的具体意思，能否说的更具体一些。';

export const ENGIN_RICH_TEXT =
  '参照上传的设计标准，对上传的计算书的[数据、结论、方法]进行冲突性校验审核';

// export const ENGIN_RICH_TEXT_PLACEHOLDER = React.createElement(
//   'p',
//   null,
//   '请您上传 ',
//   React.createElement('span', null, '设计标准'),
//   ' 与 ',
//   React.createElement('span', null, '计算书'),
//   ' 两个文件',
// );
export const ENGIN_RICH_TEXT_PLACEHOLDER = '请您上传设计标准与计算书两个文件';

// export const ENGIN_FILE_CONTENT_COMPARISON_PLACEHOLDER = React.createElement(
//   'p',
//   null,
//   '请您上传需要',
//   React.createElement('span', null, '比对文件'),
//   '和',
//   React.createElement('span', null, '基准文件'),
// );
export const ENGIN_FILE_CONTENT_COMPARISON_PLACEHOLDER = '请您上传需要比对文件和基准文件';

// export const ENGIN_REPORT_REFERENCE_STANDARD_INSPECTION = React.createElement(
//   'p',
//   null,
//   '请输入需要检测的报告内容或上传文件',
// );
export const ENGIN_REPORT_REFERENCE_STANDARD_INSPECTION = '请输入或上传需要检查的报告内容';

export const BOTTOM_TIP = '内容由AI生成，无法确保真实准确，请仔细甄别';

export const USER_ROLE = 'user';

export const ASSISTANT_ROLE = 'assistant';

export const CHAT_ROLE = {
  [ASSISTANT_ROLE]: {
    name: '瞳界',
    avatar: logo,
  },
};

export const EVENT_TYPES = {
  answer: 'answer',
  done: 'done',
  error: 'error',
};

// 最大上传文件数量
export const MAX_FILE_COUNT = 5;

// 报告引用标准校验最大上传文件数量
export const MAX_FILE_COUNT_REPORT_VALID = 1;

// 计算书审核上传数量
export const FILE_CALCULATE_THE_AUDIT_COUNT = 1;

// 文件内容对比
export const STANDARD_FILES_COUNT = 4;

export const API_KEY = 'IQZBZ-5QK63-VOD3H-OI7BT-DKQD7-U5FWG';

export const SPECIAL_NAMES = {
  文本对接: '内容合并',
  指定回复: '规范输出格式',
  'HTTP 请求': '外部接口调用',
  判断器: '逻辑判断',
  批量执行: '多任务批量执行中',
  知识库搜索引用合并: '多知识源整合查询',
  问题优化: '提高搜索精度中',
};

export const NONE_SHOW = {
  用户选择: '',
  变量更新: '',
  代码运行: '',
  'Laf函数调用（测试）': '',
  自定义反馈: '',
};

export const OFFICE_PLACEHOLDER = {
  write: { placeholder: '', fileFormat: [] },
  reading: { placeholder: '', fileFormat: [] },
  ppt: { placeholder: '请输入PPT主题内容', fileFormat: ['pdf', 'txt', 'docx'] },
  pdf: { placeholder: '', fileFormat: [] },
  image: { placeholder: '', fileFormat: [] },
  video: { placeholder: '', fileFormat: [] },
  speech: { placeholder: '', fileFormat: [] },
  tts: { placeholder: '', fileFormat: [] },
  coding: { placeholder: '', fileFormat: [] },
};
