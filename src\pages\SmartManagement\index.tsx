/**
 * 智慧管理
 */
import {
  ENGIN_FILE_CONTENT_COMPARISON_PLACEHOLDER,
  ENGIN_REPORT_REFERENCE_STANDARD_INSPECTION,
  ENGIN_RICH_TEXT_PLACEHOLDER,
} from '@/config/chat';
import { fetchNewChatId, getEnginList } from '@/services/chat';
import type { EnginItem } from '@/services/types';
import {
  deleteVisualEngineeringData,
  getVisualEngineeringList,
} from '@/services/visualEngineering';
import { dispatchInUtils } from '@/utils';
import { Button, TabPane, Tabs, Toast } from '@douyinfe/semi-ui';
import { FileItem } from '@douyinfe/semi-ui/lib/es/upload';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'umi';
import AIChat from '../Chat/Chat';
import type { QuestionsData } from '../Chat/types';
import { SmartCurrTabEnum } from '../Chat/types';
import RecordList from './components/recordList';
import UploadImage from './components/uploadImage';
import styles from './index.less';

const SmartManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const [currTab, setCurrTab] = useState<number>(0);
  const [filterType, setFilterType] = useState<EnginItem[]>([]);
  const [modeCode, setModeCode] = useState('engin');
  const [visionTabList, setVisionTabList] = useState<EnginItem[] | undefined>([]);
  // 二级code
  const [activeTab, setActiveTab] = useState<number>(0);
  const [refreshKey, setRefreshKey] = useState(0);
  //
  const developingApp = [0];
  const chatRef = useRef<any>(null);
  const recordListRef = useRef<{ loadMoreImages: () => void }>(null);
  const visionTabContentRef = useRef<HTMLDivElement>(null);
  const uploadImageRef = useRef<any>(null);
  // 获取工程列表
  useEffect(() => {
    const fetchEnginList = async () => {
      try {
        const res = await getEnginList();
        if (res.data) {
          setFilterType([...res.data]);
          if (res.data.length > 0) {
            setCurrTab(res.data[0].routeId);
            setModeCode(res.data[0].appCode);
            const visionItem = res.data.find((item: EnginItem) => item.appCode === 'vision');
            setVisionTabList(visionItem?.childrens);
            if (visionItem?.childrens?.length) {
              setActiveTab(visionItem.childrens[0].routeId);
            }
            dispatchInUtils({
              type: 'chat/setSmarInputPlaceholder',
              payload: {
                smarPlaceholder: ENGIN_RICH_TEXT_PLACEHOLDER,
              },
            });
          }
        }
      } catch (error) {
        console.error('Failed to fetch engineering list:', error);
      }
    };

    fetchEnginList();
  }, []);

  const handleTabChange = useCallback((key: string) => {
    setActiveTab(Number(key));

    const scrollableElements: Array<{
      element: Element;
      className: string;
      scrollTop: number;
    }> = [];

    const allElements = document.querySelectorAll('*');
    allElements.forEach((element) => {
      const style = window.getComputedStyle(element);
      if (
        (style.overflowY === 'auto' ||
          style.overflowY === 'scroll' ||
          style.overflow === 'auto' ||
          style.overflow === 'scroll') &&
        element.scrollHeight > element.clientHeight &&
        element.scrollTop > 0
      ) {
        scrollableElements.push({
          element,
          className: element.className,
          scrollTop: element.scrollTop,
        });
      }
    });

    if (scrollableElements.length > 0) {
      scrollableElements.forEach(({ element }) => {
        element.scrollTo(0, 0);
      });
    } else {
      if (visionTabContentRef.current) {
        visionTabContentRef.current.scrollTo(0, 0);
      }
    }
  }, []);

  const deleteTextImage = async (id: number) => {
    try {
      const { data } = await deleteVisualEngineeringData({ id });
      if (data) {
        uploadImageRef.current?.clearCertainImg(id);
        Toast.success('删除成功');
        return true;
      } else {
        Toast.error('删除失败');
        return false;
      }
    } catch (error) {
      Toast.error('删除失败');
      return false;
    }
  };

  const fetchImageData = async () => {
    try {
      const res = await getVisualEngineeringList({ routeId: activeTab });
      return res.data;
    } catch (error) {
      console.log('error ===> getVisualEngineeringList', error);
      return [];
    }
  };

  const handleRefresh = useCallback(() => {
    setRefreshKey((prev) => prev + 1);
  }, []);

  const customHandleSend = async (content: string, attachments: FileItem[]) => {
    try {
      const params = {
        appCode: modeCode, // 默认code，选择知识库后替换
      };
      const res = await fetchNewChatId(params);
      const stateData: QuestionsData = {
        content,
        attachments: JSON.stringify(attachments),
      };
      if (res.data) {
        dispatchInUtils({
          type: 'historyChat/unshiftChat',
          payload: {
            chatId: res.data,
            chatTitle: content.substring(0, 20) || '新对话',
            appCode: modeCode,
            source: '',
          },
        });

        navigate(`/chat/${res.data}?type=${modeCode}&code=${currTab}`, {
          state: stateData,
        });
      }
    } catch (error) {
      console.error('Failed to get chatId:', error);
    }
  };
  const FilterTabs = () => {
    const onNavClick = (routeId: number, appCode: string) => {
      if (developingApp.includes(routeId)) return;
      setCurrTab(routeId);
      setModeCode(appCode);
      // 获取 CustomInputRender 组件实例，清除智慧工程tab切换时的旧数据
      chatRef.current?.getInputAreaRenderRef();
    };

    return (
      <div className={styles.smartMFilterTabsButton}>
        {filterType.map((item) => (
          <Button
            key={item.routeId}
            theme={item.routeId === currTab ? 'solid' : 'light'}
            type="tertiary"
            disabled={developingApp.includes(item.routeId)}
            className={`${styles.navButton} ${item.routeId === currTab && 'active'} ${
              developingApp.includes(item.routeId) && 'developing'
            }`}
            onClick={() => onNavClick(item.routeId, item.appCode)}
          >
            {item.desc}
          </Button>
        ))}
      </div>
    );
  };
  const chatTopSlot = () => {
    const messages: { [key in number]: JSX.Element | null } = {
      [SmartCurrTabEnum.engin]: (
        <p className={styles.enginTitle}>
          对计算书中的数据、公式和结论进行准确性和规范性的全面检查
        </p>
      ),
      [SmartCurrTabEnum.report_compare]: (
        <p className={styles.reportCompareTitle}>
          支持上传一个源文件与多个基准文件，自动比对内容差异
        </p>
      ),
      [SmartCurrTabEnum.report_valid]: (
        <>
          <p className={styles.reportValidTitle}>对报告中引用的标准有效性和准确性进行检测</p>
          <p className={styles.reportValidDesc}>内容主要包括标准的编号、状态、名称及是否存在</p>
        </>
      ),
      [SmartCurrTabEnum.vision]: <></>,
    };

    // 确保 currTab 是有效的键，否则返回 null
    const defaultKey = Object.keys(messages).map(Number)[0]; // 将字符串键转换为数字
    const selectedKey = currTab !== undefined && currTab in messages ? currTab : defaultKey;

    return messages[selectedKey] || null;
  };
  const placeholderText = () => {
    // const placeholders: { [key: string]: JSX.Element } = {
    //   [SmartCurrTabEnum.engin.toString()]: ENGIN_RICH_TEXT_PLACEHOLDER,
    //   [SmartCurrTabEnum.report_compare.toString()]: ENGIN_FILE_CONTENT_COMPARISON_PLACEHOLDER,
    //   [SmartCurrTabEnum.report_valid.toString()]: ENGIN_REPORT_REFERENCE_STANDARD_INSPECTION,
    // };
    const placeholders: { [key: string]: string } = {
      [SmartCurrTabEnum.engin.toString()]: ENGIN_RICH_TEXT_PLACEHOLDER,
      [SmartCurrTabEnum.report_compare.toString()]: ENGIN_FILE_CONTENT_COMPARISON_PLACEHOLDER,
      [SmartCurrTabEnum.report_valid.toString()]: ENGIN_REPORT_REFERENCE_STANDARD_INSPECTION,
    };

    // 确保 currTab 是有效的键，否则返回默认值
    const defaultKey = Object.keys(placeholders)[0]; // 默认键为字符串
    const selectedKey =
      currTab !== undefined && placeholders[currTab.toString()] ? currTab.toString() : defaultKey;

    return placeholders[selectedKey] || '';
  };

  return (
    <div
      className={`${styles.smartManagementChat} ${
        modeCode === 'vision' ? styles.smartManagementChatVision : ''
      }`}
    >
      <div className={styles.smartMFilterTabs}>
        <FilterTabs />
        {modeCode === 'vision' && (
          <div className={styles.smartMFilterTabsTab}>
            <Tabs type="line" activeKey={String(activeTab)} onChange={handleTabChange} collapsible>
              {visionTabList &&
                visionTabList.map((item) => (
                  <TabPane tab={item.desc} itemKey={String(item.routeId)} key={item.routeId}>
                    <div ref={visionTabContentRef} className={styles.visionTabContent}>
                      <UploadImage
                        ref={uploadImageRef}
                        upRecordList={handleRefresh}
                        appCode={item.appCode}
                        routeId={item.routeId}
                      />
                      <RecordList
                        ref={recordListRef}
                        refreshKey={refreshKey}
                        fetchImages={fetchImageData}
                        active={activeTab === item.routeId}
                        onDelete={deleteTextImage}
                      />
                    </div>
                  </TabPane>
                ))}
            </Tabs>
          </div>
        )}
      </div>

      <div>
        {modeCode !== 'vision' && (
          <AIChat
            ref={chatRef}
            appCode={modeCode}
            chatTopSlot={chatTopSlot()}
            customHandleSend={customHandleSend}
            showTemplateHeader={false}
            smarPlaceholder={placeholderText()}
          />
        )}
      </div>
    </div>
  );
};

export default () => <SmartManagementPage />;
